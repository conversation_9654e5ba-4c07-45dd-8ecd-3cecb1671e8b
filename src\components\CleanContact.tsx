'use client';

import React, { useRef, useEffect, useState } from 'react';
import { motion, useInView } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

interface CleanContactProps {
  title?: string;
  subtitle?: string;
  description?: string;
}

const CleanContact: React.FC<CleanContactProps> = ({
  title = "Let's work together",
  subtitle = "Get in touch",
  description = "Ready to start your next project? We'd love to hear about your vision and discuss how we can help bring it to life."
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const formRef = useRef<HTMLFormElement>(null);
  const isInView = useInView(containerRef, { once: true, margin: "-100px" });

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  useEffect(() => {
    if (!isInView) return;

    const ctx = gsap.context(() => {
      // Slow, cinematic animation
      gsap.fromTo(titleRef.current,
        {
          opacity: 0,
          y: 30
        },
        {
          opacity: 1,
          y: 0,
          duration: 1.5,
          ease: 'power2.out',
          delay: 0.3
        }
      );

      // Form animation
      const formElements = formRef.current?.querySelectorAll('.form-field');
      if (formElements) {
        gsap.fromTo(formElements,
          {
            opacity: 0,
            y: 20
          },
          {
            opacity: 1,
            y: 0,
            duration: 1,
            stagger: 0.1,
            ease: 'power2.out',
            delay: 0.8
          }
        );
      }
    }, containerRef);

    return () => ctx.revert();
  }, [isInView]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      setSubmitStatus('success');
      setFormData({ name: '', email: '', company: '', message: '' });
    } catch (error) {
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
      setTimeout(() => setSubmitStatus('idle'), 3000);
    }
  };

  return (
    <section
      ref={containerRef}
      className="py-24 md:py-32 bg-gray-50 relative overflow-hidden"
    >
      {/* Minimal background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-48 h-48 bg-white rounded-full opacity-60" />
        <div className="absolute bottom-20 right-20 w-32 h-32 bg-gray-900 opacity-5 rotate-45" />
        <div className="absolute top-1/2 left-1/2 w-px h-64 bg-gray-200 transform -translate-x-1/2" />
      </div>

      <div className="relative z-10 max-w-4xl mx-auto px-6">
        {/* Clean Header */}
        <div className="text-center mb-16">
          <motion.p
            className="text-sm md:text-base font-medium text-gray-500 tracking-[0.2em] uppercase mb-6"
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ delay: 0.2, duration: 1 }}
          >
            {subtitle}
          </motion.p>

          <h2
            ref={titleRef}
            className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-gray-900 mb-6"
          >
            {title}
          </h2>

          <motion.p
            className="text-lg text-gray-600 font-light leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ delay: 0.6, duration: 1 }}
          >
            {description}
          </motion.p>
        </div>

        {/* Clean Contact Form */}
        <form ref={formRef} onSubmit={handleSubmit} className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="form-field">
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-3">
                Name
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                className="w-full px-0 py-3 border-0 border-b border-gray-300 bg-transparent focus:border-gray-900 focus:outline-none transition-colors duration-300 text-gray-900"
                placeholder="Your name"
              />
            </div>

            <div className="form-field">
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-3">
                Email
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                required
                className="w-full px-0 py-3 border-0 border-b border-gray-300 bg-transparent focus:border-gray-900 focus:outline-none transition-colors duration-300 text-gray-900"
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          <div className="form-field">
            <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-3">
              Company
            </label>
            <input
              type="text"
              id="company"
              name="company"
              value={formData.company}
              onChange={handleInputChange}
              className="w-full px-0 py-3 border-0 border-b border-gray-300 bg-transparent focus:border-gray-900 focus:outline-none transition-colors duration-300 text-gray-900"
              placeholder="Your company"
            />
          </div>

          <div className="form-field">
            <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-3">
              Message
            </label>
            <textarea
              id="message"
              name="message"
              value={formData.message}
              onChange={handleInputChange}
              required
              rows={6}
              className="w-full px-0 py-3 border-0 border-b border-gray-300 bg-transparent focus:border-gray-900 focus:outline-none transition-colors duration-300 text-gray-900 resize-none"
              placeholder="Tell us about your project..."
            />
          </div>

          <div className="form-field pt-8">
            <motion.button
              type="submit"
              disabled={isSubmitting}
              className="group relative px-12 py-4 bg-gray-900 text-white font-medium text-base tracking-wide transition-all duration-500 hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
              whileHover={{ y: -2 }}
              whileTap={{ y: 0 }}
            >
              <span className="relative z-10">
                {isSubmitting ? 'Sending...' : 'Send Message'}
              </span>

              {/* Clean hover effect */}
              <div className="absolute inset-0 bg-gray-800 transform -translate-x-full group-hover:translate-x-0 transition-transform duration-500" />
            </motion.button>

            {/* Status Messages */}
            {submitStatus === 'success' && (
              <motion.p
                className="mt-4 text-green-600 font-medium"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
              >
                Thank you! Your message has been sent.
              </motion.p>
            )}

            {submitStatus === 'error' && (
              <motion.p
                className="mt-4 text-red-600 font-medium"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
              >
                Something went wrong. Please try again.
              </motion.p>
            )}
          </div>
        </form>

        {/* Contact Information */}
        <div className="mt-20 pt-16 border-t border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-12 text-center">
            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-2 tracking-wider">EMAIL</h3>
              <p className="text-gray-600 font-light"><EMAIL></p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-2 tracking-wider">PHONE</h3>
              <p className="text-gray-600 font-light">+352 123 456 789</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-2 tracking-wider">LOCATION</h3>
              <p className="text-gray-600 font-light">Luxembourg City</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CleanContact;
