'use client'

import React, { useState, useEffect, useRef } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { gsap } from 'gsap'

interface NavigationItem {
  name: string
  href: string
  description: string
  dropdown?: {
    name: string
    href: string
    description: string
  }[]
}

const navigationItems: NavigationItem[] = [
  { name: 'Home', href: '/', description: 'Welcome to Winova' },
  {
    name: 'Services',
    href: '/services',
    description: 'Our creative solutions',
    dropdown: [
      { name: 'Content Creation', href: '/services/content-creation', description: 'Strategic content & video production' },
      { name: 'Photography', href: '/services/photography', description: 'Professional brand & product photography' },
      { name: 'Web Development', href: '/services/web-development', description: 'Modern websites & web applications' }
    ]
  },
  { name: 'Portfolio', href: '/portfolio', description: 'Our latest work' },
  { name: 'About', href: '/about', description: 'Our story & team' },
  { name: 'Contact', href: '/contact', description: 'Let\'s work together' }
]

export default function AdvancedNavigation() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isVisible, setIsVisible] = useState(true)
  const [isScrolled, setIsScrolled] = useState(false)
  const [lastScrollY, setLastScrollY] = useState(0)
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)
  const pathname = usePathname()
  const headerRef = useRef<HTMLElement>(null)



  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY

      // Set scrolled state
      setIsScrolled(currentScrollY > 50)

      // Hide/show header based on scroll direction
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsVisible(false)
      } else if (currentScrollY < lastScrollY) {
        setIsVisible(true)
      }

      setLastScrollY(currentScrollY)
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [lastScrollY])

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileMenuOpen(false)
  }, [pathname])

  // Animate logo on mount
  useEffect(() => {
    if (headerRef.current) {
      gsap.fromTo(
        headerRef.current.querySelector('.logo'),
        { opacity: 0, y: -20 },
        { opacity: 1, y: 0, duration: 1, delay: 0.5, ease: 'power2.out' }
      )

      gsap.fromTo(
        headerRef.current.querySelectorAll('.nav-item'),
        { opacity: 0, y: -20 },
        { opacity: 1, y: 0, duration: 0.8, stagger: 0.1, delay: 0.8, ease: 'power2.out' }
      )
    }
  }, [])

  const isActive = (href: string) => {
    if (href === '/') return pathname === href
    return pathname.startsWith(href)
  }

  return (
    <>
      {/* Normal Header - Only visible at top */}
      <motion.header
        className="fixed top-0 left-0 right-0 z-50"
        animate={{
          opacity: isScrolled ? 0 : 1
        }}
        transition={{ duration: 0.3 }}
      >
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="flex items-center justify-between h-20 lg:h-24">
            {/* Logo */}
            <Link href="/" className="logo group relative z-10">
              <motion.div
                className="text-2xl lg:text-3xl font-black text-white tracking-tight"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                style={{
                  fontFamily: 'Inter, system-ui, sans-serif',
                  fontWeight: 900,
                  letterSpacing: '-0.02em'
                }}
              >
                WINOVA
                <motion.div
                  className="absolute -bottom-1 left-0 h-0.5 bg-white origin-left"
                  initial={{ scaleX: 0 }}
                  whileHover={{ scaleX: 1 }}
                  transition={{ duration: 0.3 }}
                />
              </motion.div>
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-12">
              {navigationItems.map((item) => (
                <div
                  key={item.name}
                  className="relative"
                  onMouseEnter={() => item.dropdown && setActiveDropdown(item.name)}
                  onMouseLeave={() => setActiveDropdown(null)}
                >
                  <Link
                    href={item.href}
                    className="nav-item group relative flex items-center"
                  >
                    <motion.span
                      className={`text-sm font-medium tracking-wide uppercase transition-all duration-300 ${
                        isActive(item.href)
                          ? 'text-white'
                          : 'text-gray-400 hover:text-white'
                      }`}
                      whileHover={{ y: -2 }}
                      transition={{ duration: 0.2 }}
                    >
                      {item.name}
                    </motion.span>

                    {/* Dropdown arrow */}
                    {item.dropdown && (
                      <motion.svg
                        className="ml-1 w-3 h-3 text-gray-400 group-hover:text-white transition-colors duration-300"
                        animate={{ rotate: activeDropdown === item.name ? 180 : 0 }}
                        transition={{ duration: 0.2 }}
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </motion.svg>
                    )}

                    {/* Active indicator */}
                    {isActive(item.href) && (
                      <motion.div
                        className="absolute -bottom-2 left-1/2 w-1 h-1 bg-white rounded-full"
                        layoutId="activeIndicator"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        style={{ x: '-50%' }}
                      />
                    )}

                    {/* Hover effect */}
                    <motion.div
                      className="absolute -bottom-2 left-0 right-0 h-px bg-white/30 origin-center"
                      initial={{ scaleX: 0 }}
                      whileHover={{ scaleX: 1 }}
                      transition={{ duration: 0.3 }}
                    />
                  </Link>

                  {/* Dropdown Menu */}
                  <AnimatePresence>
                    {item.dropdown && activeDropdown === item.name && (
                      <motion.div
                        className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 w-80 bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 overflow-hidden"
                        initial={{ opacity: 0, y: 10, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        exit={{ opacity: 0, y: 10, scale: 0.95 }}
                        transition={{ duration: 0.2 }}
                        style={{
                          boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.1)'
                        }}
                      >
                        <div className="p-6">
                          {item.dropdown.map((dropdownItem, index) => (
                            <Link
                              key={dropdownItem.name}
                              href={dropdownItem.href}
                              className="block group"
                            >
                              <motion.div
                                className="p-4 rounded-xl hover:bg-gray-50 transition-colors duration-200"
                                initial={{ opacity: 0, x: -10 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: index * 0.05 }}
                              >
                                <div className="font-semibold text-gray-900 group-hover:text-gray-700 transition-colors duration-200">
                                  {dropdownItem.name}
                                </div>
                                <div className="text-sm text-gray-600 mt-1">
                                  {dropdownItem.description}
                                </div>
                              </motion.div>
                            </Link>
                          ))}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              ))}
            </nav>

            {/* CTA Button */}
            <div className="hidden lg:block">
              <Link href="/contact">
                <motion.button
                  className="relative px-8 py-3 bg-white text-black font-bold text-sm tracking-wide uppercase overflow-hidden group"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  style={{
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(255, 255, 255, 0.1)'
                  }}
                >
                  <span className="relative z-10">Get Started</span>
                  <motion.div
                    className="absolute inset-0 bg-gray-100 origin-left"
                    initial={{ scaleX: 0 }}
                    whileHover={{ scaleX: 1 }}
                    transition={{ duration: 0.4 }}
                  />
                </motion.button>
              </Link>
            </div>

            {/* Mobile Menu Button */}
            <button
              className="lg:hidden relative w-8 h-8 flex flex-col justify-center items-center group"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              aria-label="Toggle mobile menu"
            >
              <motion.span
                className="w-6 h-0.5 bg-white transition-all duration-300"
                animate={{
                  rotate: isMobileMenuOpen ? 45 : 0,
                  y: isMobileMenuOpen ? 0 : -4
                }}
              />
              <motion.span
                className="w-6 h-0.5 bg-white transition-all duration-300"
                animate={{
                  opacity: isMobileMenuOpen ? 0 : 1
                }}
              />
              <motion.span
                className="w-6 h-0.5 bg-white transition-all duration-300"
                animate={{
                  rotate: isMobileMenuOpen ? -45 : 0,
                  y: isMobileMenuOpen ? 0 : 4
                }}
              />
            </button>
          </div>
        </div>
      </motion.header>

      {/* Floating Pill Header - Only visible when scrolled and scrolling up */}
      <AnimatePresence>
        {isScrolled && isVisible && (
          <motion.div
            className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50"
            initial={{ opacity: 0, y: -50, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -50, scale: 0.8 }}
            transition={{ duration: 0.4, ease: [0.25, 0.46, 0.45, 0.94] }}
          >
            <div
              className="bg-white/95 backdrop-blur-xl rounded-full px-8 py-3 shadow-2xl border border-white/20"
              style={{
                boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.1)'
              }}
            >
              <div className="flex items-center space-x-8">
                {/* Compact Logo */}
                <Link href="/">
                  <motion.div
                    className="text-lg font-black text-gray-900 tracking-tight"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    style={{
                      fontFamily: 'Inter, system-ui, sans-serif',
                      fontWeight: 900,
                      letterSpacing: '-0.02em'
                    }}
                  >
                    WINOVA
                  </motion.div>
                </Link>

                {/* Compact Navigation */}
                <nav className="hidden md:flex items-center space-x-6">
                  {navigationItems.filter(item => item.name !== 'Services').slice(0, 3).map((item) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      className="group relative"
                    >
                      <motion.span
                        className={`text-xs font-medium tracking-wide uppercase transition-all duration-300 ${
                          isActive(item.href)
                            ? 'text-gray-900'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                        whileHover={{ y: -1 }}
                        transition={{ duration: 0.2 }}
                      >
                        {item.name}
                      </motion.span>

                      {/* Active indicator */}
                      {isActive(item.href) && (
                        <motion.div
                          className="absolute -bottom-1 left-1/2 w-1 h-1 bg-gray-900 rounded-full"
                          layoutId="pillActiveIndicator"
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          style={{ x: '-50%' }}
                        />
                      )}
                    </Link>
                  ))}
                </nav>

                {/* Compact CTA */}
                <Link href="/contact">
                  <motion.button
                    className="bg-gray-900 text-white px-6 py-2 rounded-full text-xs font-bold tracking-wide uppercase"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Contact
                  </motion.button>
                </Link>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            className="fixed inset-0 z-40 lg:hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            {/* Backdrop */}
            <motion.div
              className="absolute inset-0 bg-black/90 backdrop-blur-xl"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setIsMobileMenuOpen(false)}
            />

            {/* Menu Content */}
            <motion.div
              className="relative flex flex-col items-center justify-center h-full px-6"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              transition={{ duration: 0.4, ease: [0.25, 0.46, 0.45, 0.94] }}
            >
              <nav className="space-y-8 text-center">
                {navigationItems.map((item, index) => (
                  <motion.div
                    key={item.name}
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.5 }}
                  >
                    <Link
                      href={item.href}
                      className="block group"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <motion.div
                        className={`text-4xl font-bold mb-2 transition-colors duration-300 ${
                          isActive(item.href) ? 'text-white' : 'text-gray-400 group-hover:text-white'
                        }`}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        {item.name}
                      </motion.div>
                      <div className="text-sm text-gray-500 group-hover:text-gray-300 transition-colors duration-300">
                        {item.description}
                      </div>
                    </Link>
                  </motion.div>
                ))}
              </nav>

              {/* Mobile CTA */}
              <motion.div
                className="mt-12"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6, duration: 0.5 }}
              >
                <Link href="/contact" onClick={() => setIsMobileMenuOpen(false)}>
                  <motion.button
                    className="px-12 py-4 bg-white text-black font-bold text-lg tracking-wide uppercase"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Get Started
                  </motion.button>
                </Link>
              </motion.div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}
