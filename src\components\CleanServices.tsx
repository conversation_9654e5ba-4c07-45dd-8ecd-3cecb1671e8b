'use client';

import React, { useEffect, useRef, useState } from 'react';
import Link from 'next/link';
import { motion, useInView } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

interface Service {
  title: string;
  description: string;
  number: string;
  link?: string;
}

interface CleanServicesProps {
  title?: string;
  subtitle?: string;
  services?: Service[];
}

const defaultServices: Service[] = [
  {
    title: "Content Creation",
    description: "Strategic content development including copywriting, video production, and social media content that engages your audience and drives results.",
    number: "01",
    link: "/services/content-creation"
  },
  {
    title: "Photography",
    description: "Professional photography services including product photography, corporate headshots, and brand imagery that tells your story.",
    number: "02",
    link: "/services/photography"
  },
  {
    title: "Web Development",
    description: "Modern, responsive websites built with cutting-edge technology to deliver exceptional user experiences and drive business growth.",
    number: "03",
    link: "/services/web-development"
  }
];

const CleanServices: React.FC<CleanServicesProps> = ({
  title = "Our Expertise",
  subtitle = "What we do",
  services = defaultServices
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const servicesRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(containerRef, { once: true, margin: "-100px" });
  const [hoveredService, setHoveredService] = useState<number | null>(null);

  useEffect(() => {
    if (!isInView) return;

    const ctx = gsap.context(() => {
      // Slow, cinematic title animation
      gsap.fromTo(titleRef.current,
        {
          opacity: 0,
          y: 30
        },
        {
          opacity: 1,
          y: 0,
          duration: 1.5,
          ease: 'power2.out',
          delay: 0.3
        }
      );

      // Stagger animation for services
      const serviceItems = servicesRef.current?.children;
      if (serviceItems) {
        gsap.fromTo(serviceItems,
          {
            opacity: 0,
            y: 40
          },
          {
            opacity: 1,
            y: 0,
            duration: 1.2,
            stagger: 0.2,
            ease: 'power2.out',
            delay: 0.8
          }
        );
      }
    }, containerRef);

    return () => ctx.revert();
  }, [isInView]);

  return (
    <section
      ref={containerRef}
      className="py-24 md:py-32 bg-white relative overflow-hidden"
    >
      {/* Minimal background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-32 left-20 w-64 h-64 bg-gray-50 rounded-full opacity-40" />
        <div className="absolute bottom-20 right-20 w-32 h-32 bg-gray-900 opacity-5 rotate-45" />
        <div className="absolute top-1/2 right-1/4 w-px h-48 bg-gray-200" />
      </div>

      <div className="relative z-10 max-w-6xl mx-auto px-6">
        {/* Clean Header */}
        <div className="text-center mb-20">
          <motion.p
            className="text-base md:text-lg font-semibold text-gray-500 tracking-[0.2em] uppercase mb-6"
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ delay: 0.2, duration: 1 }}
          >
            {subtitle}
          </motion.p>

          <h2
            ref={titleRef}
            className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-gray-900"
          >
            {title}
          </h2>
        </div>

        {/* Services List */}
        <div ref={servicesRef} className="space-y-0">
          {services.map((service, index) => {
            const content = (
              <motion.div
                className="group border-b border-gray-200 py-12 cursor-pointer"
                onMouseEnter={() => setHoveredService(index)}
                onMouseLeave={() => setHoveredService(null)}
                whileHover={{ x: 10 }}
                transition={{ duration: 0.4, ease: "easeOut" }}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 pr-8">
                    <div className="flex items-baseline mb-4">
                      <span className="text-sm font-medium text-gray-400 mr-6 tracking-wider">
                        {service.number}
                      </span>
                      <h3 className={`text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold transition-all duration-500 ${
                        hoveredService === index
                          ? 'text-gray-900'
                          : 'text-gray-600'
                      }`}>
                        {service.title}
                      </h3>
                    </div>

                    <motion.div
                      className="overflow-hidden"
                      initial={{ height: 0 }}
                      animate={{
                        height: hoveredService === index ? 'auto' : 0
                      }}
                      transition={{ duration: 0.5, ease: "easeInOut" }}
                    >
                      <p className="text-gray-600 text-lg leading-relaxed font-medium max-w-2xl pb-4">
                        {service.description}
                      </p>
                    </motion.div>
                  </div>

                  {/* Minimal arrow indicator */}
                  <motion.div
                    className="flex items-center justify-center w-12 h-12"
                    animate={{
                      rotate: hoveredService === index ? 45 : 0,
                      scale: hoveredService === index ? 1.1 : 1
                    }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className={`w-6 h-px transition-all duration-300 ${
                      hoveredService === index ? 'bg-gray-900' : 'bg-gray-400'
                    }`} />
                    <div className={`w-px h-6 absolute transition-all duration-300 ${
                      hoveredService === index ? 'bg-gray-900' : 'bg-gray-400'
                    }`} />
                  </motion.div>
                </div>
              </motion.div>
            );

            return service.link ? (
              <Link key={index} href={service.link}>
                {content}
              </Link>
            ) : (
              <div key={index}>
                {content}
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default CleanServices;
