'use client';

import React, { useEffect, useRef, useState } from 'react';
import { motion, useInView } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

interface WorkItem {
  id: string;
  title: string;
  category: 'Photography' | 'Video Production' | 'Digital Experiences';
  description: string;
  image: string;
  tags: string[];
  year: string;
  isVideo?: boolean;
}

interface CreativeWorkProps {
  title?: string;
  subtitle?: string;
  maxItems?: number;
}

const workItems: WorkItem[] = [
  // Photography Examples
  {
    id: '1',
    title: 'Corporate Brand Photography',
    category: 'Photography',
    description: 'Professional corporate photography capturing the essence of modern business environments.',
    image: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=800&h=600&fit=crop&crop=entropy&auto=format&q=85',
    tags: ['Corporate', 'Branding', 'Professional'],
    year: '2024'
  },
  {
    id: '2',
    title: 'Product Photography Series',
    category: 'Photography',
    description: 'High-end product photography showcasing luxury items with dramatic lighting and composition.',
    image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800&h=600&fit=crop&crop=entropy&auto=format&q=85',
    tags: ['Product', 'Commercial', 'Luxury'],
    year: '2024'
  },
  // Video Production Examples
  {
    id: '3',
    title: 'Brand Story Documentary',
    category: 'Video Production',
    description: 'Cinematic brand storytelling that captures the human side of business innovation.',
    image: 'https://images.unsplash.com/photo-1492691527719-9d1e07e534b4?w=800&h=600&fit=crop&crop=entropy&auto=format&q=85',
    tags: ['Documentary', 'Branding', 'Storytelling'],
    year: '2024',
    isVideo: true
  },
  {
    id: '4',
    title: 'Commercial Video Campaign',
    category: 'Video Production',
    description: 'Dynamic commercial content designed to engage audiences and drive brand awareness.',
    image: 'https://images.unsplash.com/photo-1574717024653-61fd2cf4d44d?w=800&h=600&fit=crop&crop=entropy&auto=format&q=85',
    tags: ['Commercial', 'Marketing', 'Campaign'],
    year: '2024',
    isVideo: true
  },
  // Digital Experiences
  {
    id: '5',
    title: 'Winova Agency Website',
    category: 'Digital Experiences',
    description: 'This very website - a clean, minimal design showcasing our development capabilities.',
    image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop&crop=entropy&auto=format&q=85',
    tags: ['Web Development', 'Design', 'Agency'],
    year: '2024'
  },
  {
    id: '6',
    title: 'Interactive Design Concepts',
    category: 'Digital Experiences',
    description: 'Conceptual web designs demonstrating our approach to user experience and interface design.',
    image: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=800&h=600&fit=crop&crop=entropy&auto=format&q=85',
    tags: ['UX/UI', 'Concept', 'Interactive'],
    year: '2024'
  }
];

const CreativeWork: React.FC<CreativeWorkProps> = ({
  title = "Our Creative Work",
  subtitle = "Photography • Video • Digital Experiences",
  maxItems = 6
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const workRef = useRef<HTMLDivElement>(null);
  const [activeCategory, setActiveCategory] = useState<string>('All');
  const [filteredItems, setFilteredItems] = useState<WorkItem[]>(workItems.slice(0, maxItems));
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const isInView = useInView(containerRef, { once: true, margin: "-100px" });

  const categories = ['All', 'Photography', 'Video Production', 'Digital Experiences'];
  


  useEffect(() => {
    if (activeCategory === 'All') {
      setFilteredItems(workItems.slice(0, maxItems));
    } else {
      setFilteredItems(workItems.filter(item => item.category === activeCategory).slice(0, maxItems));
    }
  }, [activeCategory, maxItems]);

  useEffect(() => {
    if (!isInView) return;

    const ctx = gsap.context(() => {
      // Title animation
      gsap.fromTo(titleRef.current,
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: 1.5,
          ease: "power2.out",
          delay: 0.3
        }
      );

      // Subtitle animation
      gsap.fromTo(subtitleRef.current,
        { opacity: 0, y: 30 },
        {
          opacity: 1,
          y: 0,
          duration: 1.2,
          ease: "power2.out",
          delay: 0.6
        }
      );

      // Work items animation
      const workCards = workRef.current?.children;
      if (workCards) {
        gsap.fromTo(workCards,
          { opacity: 0, y: 60, scale: 0.9 },
          {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: 1.2,
            stagger: 0.15,
            ease: "power2.out",
            delay: 0.9
          }
        );
      }
    }, containerRef);

    return () => ctx.revert();
  }, [isInView, filteredItems]);

  return (
    <section
      ref={containerRef}
      className="py-24 md:py-32 bg-gray-50 relative overflow-hidden"
    >
      {/* Clean background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-64 h-64 bg-white rounded-full opacity-40" />
        <div className="absolute bottom-20 right-20 w-32 h-32 bg-gray-900 opacity-5" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-16">
          <motion.h2
            ref={titleRef}
            className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-gray-900 mb-6"
            initial={{ opacity: 0, y: 50 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
            transition={{ duration: 1.5, ease: "easeOut", delay: 0.3 }}
          >
            {title}
          </motion.h2>
          
          <motion.p
            ref={subtitleRef}
            className="text-lg md:text-xl text-gray-500 tracking-[0.2em] uppercase"
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 1.2, ease: "easeOut", delay: 0.6 }}
          >
            {subtitle}
          </motion.p>
        </div>

        {/* Enhanced Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-16">
          {categories.map((category) => (
            <motion.button
              key={category}
              onClick={() => setActiveCategory(category)}
              className={`px-6 py-3 font-medium text-sm tracking-wide transition-all duration-300 ${
                activeCategory === category
                  ? 'bg-gray-900 text-white'
                  : 'bg-white text-gray-600 hover:bg-gray-100'
              }`}
              whileHover={{
                scale: 1.05,
                y: -2
              }}
              whileTap={{
                scale: 0.95
              }}
              transition={{ duration: 0.2 }}
            >
              {category}
            </motion.button>
          ))}
        </div>

        {/* Work Grid */}
        <div ref={workRef} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredItems.map((item, index) => (
            <motion.div
              key={item.id}
              className="group cursor-pointer relative"
              initial={{ opacity: 0, y: 60, scale: 0.9 }}
              animate={isInView ? { opacity: 1, y: 0, scale: 1 } : { opacity: 0, y: 60, scale: 0.9 }}
              transition={{ duration: 1.2, delay: 0.9 + (index * 0.15), ease: "easeOut" }}
              onMouseEnter={() => setHoveredItem(item.id)}
              onMouseLeave={() => setHoveredItem(null)}
              whileHover={{
                y: -8,
                scale: 1.02
              }}
            >
              {/* Image Container */}
              <div className="relative overflow-hidden bg-gray-200 aspect-[4/3] mb-6 rounded-lg">
                <img
                  src={item.image}
                  alt={item.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
                

                
                {/* Video Play Button */}
                {item.isVideo && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <motion.div
                      className="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center backdrop-blur-sm"
                      whileHover={{
                        scale: 1.1
                      }}
                      whileTap={{
                        scale: 0.9
                      }}
                      transition={{ duration: 0.3 }}
                    >
                      <svg 
                        className="w-6 h-6 text-gray-900 ml-1" 
                        fill="currentColor" 
                        viewBox="0 0 24 24"
                      >
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </motion.div>
                  </div>
                )}

                {/* Category Badge */}
                <div className="absolute top-4 left-4 bg-white bg-opacity-90 backdrop-blur-sm px-3 py-1 text-xs font-medium text-gray-800 tracking-wide">
                  {item.category}
                </div>

                {/* Year Badge */}
                <div className="absolute top-4 right-4 bg-gray-900 bg-opacity-80 text-white px-3 py-1 text-xs font-medium">
                  {item.year}
                </div>
              </div>

              {/* Content */}
              <div className="space-y-3 relative z-10">
                <h3 className="text-xl md:text-2xl font-bold text-gray-900 transition-colors duration-300">
                  {item.title}
                </h3>
                
                <p className="text-gray-600 leading-relaxed">
                  {item.description}
                </p>

                {/* Tags */}
                <div className="flex flex-wrap gap-2 pt-2">
                  {item.tags.map((tag, tagIndex) => (
                    <span
                      key={tagIndex}
                      className="text-xs font-medium text-gray-500 bg-gray-100 px-2 py-1 tracking-wide rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          className="text-center mt-20"
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 1, delay: 1.5, ease: "easeOut" }}
        >
          <p className="text-lg text-gray-600 mb-8">
            Ready to create something amazing together?
          </p>
          <motion.button
            className="inline-flex items-center px-8 py-4 bg-gray-900 text-white font-semibold text-lg tracking-wide transition-all duration-300 hover:bg-gray-800 group"
            whileHover={{ y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            Start Your Project
            <svg
              className="ml-3 w-5 h-5 transform group-hover:translate-x-1 transition-transform duration-300"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 8l4 4m0 0l-4 4m4-4H3"
              />
            </svg>
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
};

export default CreativeWork;
