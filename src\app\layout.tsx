import type { Metada<PERSON> } from "next";
import { Inter, JetBrains_Mono } from "next/font/google";
import { SmoothScrollProvider } from "@/components/providers/SmoothScrollProvider";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
  preload: true,
});

const jetbrainsMono = JetBrains_Mono({
  variable: "--font-mono",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: {
    default: "Winova | Creative Agency Luxembourg",
    template: "%s | Winova",
  },
  description: "Premium creative agency in Luxembourg specializing in content creation, professional photography for real estate and restaurants, and modern web development services.",
  keywords: [
    "creative agency luxembourg",
    "photography luxembourg", 
    "web development luxembourg",
    "content creation",
    "real estate photography",
    "restaurant photography",
    "local business photography"
  ],
  authors: [{ name: "Winova Creative Agency" }],
  creator: "<PERSON><PERSON>",
  publisher: "<PERSON><PERSON>",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://winova.lu",
    siteName: "Winova",
    title: "Winova | Creative Agency Luxembourg",
    description: "Premium creative agency in Luxembourg specializing in content creation, professional photography, and web development.",
    images: [
      {
        url: "https://winova.lu/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Winova Creative Agency Luxembourg",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Winova | Creative Agency Luxembourg",
    description: "Premium creative agency in Luxembourg specializing in content creation, professional photography, and web development.",
    images: ["https://winova.lu/og-image.jpg"],
    creator: "@winova_lu",
  },
  icons: {
    icon: "/favicon.ico",
    shortcut: "/favicon-16x16.png",
    apple: "/apple-touch-icon.png",
  },
  manifest: "/site.webmanifest",
  metadataBase: new URL("https://winova.lu"),
  alternates: {
    canonical: "https://winova.lu",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${inter.variable} ${jetbrainsMono.variable} font-sans antialiased`}
        suppressHydrationWarning={true}
      >
        <SmoothScrollProvider>
          <div className="relative min-h-screen bg-background text-foreground">
            {children}
          </div>
        </SmoothScrollProvider>
      </body>
    </html>
  );
}
